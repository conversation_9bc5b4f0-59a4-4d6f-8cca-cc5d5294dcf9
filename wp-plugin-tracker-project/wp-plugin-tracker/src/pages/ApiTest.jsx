import React, { useState } from "react";
import { toast } from "react-hot-toast";

const ApiTest = () => {
  const [result, setResult] = useState("");
  const [authToken, setAuthToken] = useState(null);
  const [loading, setLoading] = useState(false);

  const API_BASE = import.meta.env.VITE_API_URL || "http://localhost:5002/api";

  const clearResults = () => {
    setResult("");
  };

  const testLogin = async () => {
    setLoading(true);
    setResult("Testing login...");

    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: "admin",
          password: "password",
        }),
      });

      const data = await response.json();

      if (data.success && data.token) {
        setAuthToken(data.token);
        setResult(
          `✅ Login successful!\nToken: ${data.token.substring(
            0,
            20
          )}...\nUser: ${data.user.name}`
        );
        toast.success("Login successful!");
      } else {
        setResult(`❌ Login failed:\n${JSON.stringify(data, null, 2)}`);
        toast.error("Login failed");
      }
    } catch (error) {
      setResult(`❌ Login error: ${error.message}`);
      toast.error(`Login error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPlugins = async () => {
    if (!authToken) {
      setResult("❌ Please login first");
      toast.error("Please login first");
      return;
    }

    setLoading(true);
    setResult("Testing plugins API...");

    try {
      const response = await fetch(`${API_BASE}/plugins?search=`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (data.success) {
        setResult(
          `✅ Plugins API successful!\n\nCount: ${
            data.count
          }\n\nPlugins:\n${data.data
            .map(
              (plugin) =>
                `- ${plugin.name}\n  Slug: ${
                  plugin.slug
                }\n  Downloads: ${plugin.downloads?.toLocaleString()}\n  Rating: ${
                  plugin.rating
                }\n  Version: ${plugin.version}\n`
            )
            .join("\n")}`
        );
        toast.success(`Found ${data.count} plugins`);
      } else {
        setResult(`❌ Plugins API failed:\n${JSON.stringify(data, null, 2)}`);
        toast.error("Plugins API failed");
      }
    } catch (error) {
      setResult(`❌ Plugins API error: ${error.message}`);
      toast.error(`Plugins API error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testKeywords = async () => {
    if (!authToken) {
      setResult("❌ Please login first");
      toast.error("Please login first");
      return;
    }

    setLoading(true);
    setResult("Testing keywords API...");

    try {
      // Get EmbedPress plugin ID first
      const pluginsResponse = await fetch(`${API_BASE}/plugins?search=`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      });

      const pluginsData = await pluginsResponse.json();
      const embedpress = pluginsData.data.find((p) => p.slug === "embedpress");

      if (!embedpress) {
        setResult("❌ EmbedPress plugin not found");
        toast.error("EmbedPress plugin not found");
        return;
      }

      // Get keywords for EmbedPress
      const keywordsResponse = await fetch(
        `${API_BASE}/keywords/plugin/${embedpress._id}`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const keywordsData = await keywordsResponse.json();

      if (keywordsData.success) {
        setResult(
          `✅ Keywords API successful!\n\nCount: ${
            keywordsData.count
          }\n\nKeywords for EmbedPress:\n${keywordsData.data
            .map(
              (keyword) =>
                `- "${keyword.keyword}"\n  Rank: #${
                  keyword.currentRank
                }\n  Occurrences: ${keyword.occurrences}\n  Updated: ${new Date(
                  keyword.updated
                ).toLocaleString()}\n`
            )
            .join("\n")}`
        );
        toast.success(`Found ${keywordsData.count} keywords`);
      } else {
        setResult(
          `❌ Keywords API failed:\n${JSON.stringify(keywordsData, null, 2)}`
        );
        toast.error("Keywords API failed");
      }
    } catch (error) {
      setResult(`❌ Keywords API error: ${error.message}`);
      toast.error(`Keywords API error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 API Test Tool
          </h1>

          <p className="text-gray-600 mb-6">
            This tool tests the actual API responses to verify what data is
            being returned.
          </p>

          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={testLogin}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
            >
              {loading ? "Testing..." : "1. Test Login"}
            </button>
            <button
              onClick={testPlugins}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
            >
              {loading ? "Testing..." : "2. Test Plugins API"}
            </button>
            <button
              onClick={testKeywords}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
            >
              {loading ? "Testing..." : "3. Test Keywords API"}
            </button>
            <button
              onClick={clearResults}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
            >
              Clear Results
            </button>
          </div>

          {result && (
            <div
              className={`p-4 rounded-md font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto ${
                result.includes("✅")
                  ? "bg-green-50 border border-green-200 text-green-800"
                  : result.includes("❌")
                  ? "bg-red-50 border border-red-200 text-red-800"
                  : "bg-gray-50 border border-gray-200 text-gray-800"
              }`}
            >
              {result}
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="font-semibold text-blue-900 mb-2">API Base URL:</h3>
            <code className="text-blue-800">{API_BASE}</code>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTest;
